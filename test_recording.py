#!/usr/bin/env python3
"""
Test script to demonstrate the recording functionality.
This script will show the main window and simulate a recording trigger.
"""

import sys
import os
sys.path.append('src')

from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

# Import the main app components
from main import Whisper<PERSON>riterApp
from ui.fluid_recording_window import FluidRecordingWindow


class TestWindow(QWidget):
    """Test window to demonstrate the recording functionality."""
    
    def __init__(self):
        super().__init__()
        self.app_instance = None
        self.init_ui()
        
    def init_ui(self):
        """Initialize the test UI."""
        self.setWindowTitle("WhisperWriter Test")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("WhisperWriter Test")
        title.setFont(QFont('Segoe UI', 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        
        # Instructions
        instructions = QLabel("""
This will test the WhisperWriter recording functionality.

1. Click 'Start App' to launch the main application
2. The main window will appear (settings/control interface)
3. Click 'Test Recording' to show the recording window
4. Or press Ctrl+Shift+Space to trigger recording

The recording window will show real voice levels!
        """)
        instructions.setFont(QFont('Segoe UI', 10))
        instructions.setWordWrap(True)
        
        # Buttons
        start_btn = QPushButton("Start App")
        start_btn.setFont(QFont('Segoe UI', 12))
        start_btn.setFixedHeight(40)
        start_btn.clicked.connect(self.start_app)
        
        test_btn = QPushButton("Test Recording Window")
        test_btn.setFont(QFont('Segoe UI', 12))
        test_btn.setFixedHeight(40)
        test_btn.clicked.connect(self.test_recording)
        
        layout.addWidget(title)
        layout.addWidget(instructions)
        layout.addWidget(start_btn)
        layout.addWidget(test_btn)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def start_app(self):
        """Start the main WhisperWriter application."""
        try:
            print("Starting WhisperWriter application...")
            self.app_instance = WhisperWriterApp()
            print("Application started! Main window should be visible.")
            print("Press Ctrl+Shift+Space to start recording!")
        except Exception as e:
            print(f"Error starting app: {e}")
            
    def test_recording(self):
        """Test the recording window directly."""
        try:
            print("Testing recording window...")
            self.recording_window = FluidRecordingWindow()
            self.recording_window.position_center_screen()
            self.recording_window.start_recording()
            
            # Simulate voice levels for demo
            self.timer = QTimer()
            def update_level():
                import random
                level = random.randint(0, 50)
                self.recording_window.update_audio_level(level)
            self.timer.timeout.connect(update_level)
            self.timer.start(50)
            
            print("Recording window shown with simulated voice levels!")
        except Exception as e:
            print(f"Error testing recording: {e}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    print("=" * 50)
    print("WhisperWriter Test Application")
    print("=" * 50)
    print()
    print("This test will help you understand how WhisperWriter works:")
    print()
    print("1. MAIN WINDOW: Settings and control interface")
    print("   - Shows when you run the app")
    print("   - Contains settings, history, and instructions")
    print()
    print("2. RECORDING WINDOW: Modern recording interface")
    print("   - Shows when you press Ctrl+Shift+Space")
    print("   - Displays real-time voice levels")
    print("   - Has Stop/Cancel buttons")
    print()
    print("Default hotkey: Ctrl+Shift+Space")
    print("=" * 50)
    print()
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())
