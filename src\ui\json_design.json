{"designSystem": {"name": "Voice Recording Interface", "version": "1.0", "description": "Design system for voice recording and transcription interface with dark theme"}, "colorPalette": {"backgrounds": {"primary": "#2C2C2E", "secondary": "#1C1C1E", "overlay": "rgba(44, 44, 46, 0.95)", "card": "#3A3A3C"}, "interactive": {"recordingRed": "#FF3B30", "processingBlue": "#007AFF", "successGreen": "#30D158", "accentBlue": "#0A84FF"}, "text": {"primary": "#FFFFFF", "secondary": "#EBEBF5", "tertiary": "#8E8E93", "placeholder": "#6D6D70"}, "borders": {"subtle": "#48484A", "medium": "#636366", "separator": "#38383A"}}, "elementStyling": {"mainContainer": {"background": "#2C2C2E", "borderRadius": "16px", "shadow": "0 8px 32px rgba(0, 0, 0, 0.3)", "border": "1px solid #48484A", "padding": "24px", "minWidth": "600px", "maxWidth": "900px"}, "waveformVisualization": {"container": {"background": "transparent", "padding": "32px 0", "height": "120px", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "bars": {"idle": {"fill": "#48484A", "opacity": "0.6", "width": "2px", "spacing": "1px", "height": "varies 4px-40px"}, "recording": {"fill": "#FF3B30", "opacity": "1.0", "width": "2px", "spacing": "1px", "height": "varies 8px-60px", "animation": "pulse 0.5s ease-in-out infinite alternate"}, "processing": {"fill": "#007AFF", "opacity": "0.8", "width": "2px", "spacing": "1px", "height": "varies 12px-80px", "animation": "wave 1.2s ease-in-out infinite"}}}, "statusIndicator": {"container": {"display": "flex", "alignItems": "center", "gap": "12px", "marginBottom": "24px"}, "icon": {"size": "20px", "borderRadius": "50%"}, "recording": {"icon": {"background": "#FF3B30", "animation": "pulse 1s ease-in-out infinite"}, "text": {"color": "#FFFFFF", "fontWeight": "500", "fontSize": "16px"}}, "processing": {"icon": {"background": "#007AFF", "animation": "spin 1s linear infinite"}, "text": {"color": "#FFFFFF", "fontWeight": "500", "fontSize": "16px"}}, "done": {"icon": {"background": "#30D158", "fill": "#FFFFFF"}, "text": {"color": "#FFFFFF", "fontWeight": "500", "fontSize": "16px"}}}, "transcriptionArea": {"container": {"background": "transparent", "padding": "24px 0", "minHeight": "100px", "textAlign": "center"}, "text": {"color": "#FFFFFF", "fontSize": "18px", "lineHeight": "1.5", "fontWeight": "400", "letterSpacing": "0.01em"}, "greeting": {"color": "#FFFFFF", "fontSize": "24px", "fontWeight": "600", "marginBottom": "16px"}}, "controlBar": {"container": {"background": "transparent", "borderTop": "1px solid #38383A", "padding": "16px 0 0 0", "display": "flex", "alignItems": "center", "justifyContent": "space-between"}, "leftSection": {"display": "flex", "alignItems": "center", "gap": "16px"}, "rightSection": {"display": "flex", "alignItems": "center", "gap": "24px"}}, "actionButtons": {"Mode": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 0", "hover": {"color": "#FFFFFF"}}, "keyboard": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 0", "display": "flex", "alignItems": "center", "gap": "4px", "hover": {"color": "#FFFFFF"}}, "stop": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 16px", "hover": {"color": "#FF3B30"}}, "record": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 16px", "hover": {"color": "#FF3B30"}}, "close": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 16px", "hover": {"color": "#FFFFFF"}}, "cancel": {"background": "transparent", "border": "none", "color": "#EBEBF5", "fontSize": "16px", "fontWeight": "400", "cursor": "pointer", "padding": "8px 16px", "hover": {"color": "#FF3B30"}}}, "keyboardShortcuts": {"container": {"display": "flex", "alignItems": "center", "gap": "4px"}, "key": {"background": "#48484A", "color": "#EBEBF5", "padding": "2px 6px", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "500", "fontFamily": "SF Mono, Monaco, monospace"}, "modifier": {"background": "#48484A", "color": "#EBEBF5", "padding": "2px 6px", "borderRadius": "4px", "fontSize": "12px", "fontWeight": "500"}}}, "stateManagement": {"idle": {"waveform": "subtle bars with low opacity", "statusIcon": "none", "statusText": "none", "controls": ["Mode", "⌘K", "Record", "Space", "Close", "Esc"]}, "recording": {"waveform": "animated red bars with varying heights", "statusIcon": "pulsing red circle", "statusText": "Recording", "controls": ["Mode", "⌘K", "Stop", "Space", "Cancel", "Esc"]}, "processing": {"waveform": "animated blue bars with wave motion", "statusIcon": "spinning blue circle", "statusText": "Processing", "controls": ["Mode", "⌘K", "Record", "Space", "Close", "Esc"], "transcription": "appears progressively"}, "complete": {"waveform": "static bars", "statusIcon": "green checkmark circle", "statusText": "Done", "controls": ["Mode", "⌘K", "Record", "Space", "Close", "Esc"], "transcription": "full text visible"}}, "animations": {"waveformPulse": {"duration": "0.5s", "easing": "ease-in-out", "direction": "alternate", "iterations": "infinite"}, "waveformWave": {"duration": "1.2s", "easing": "ease-in-out", "iterations": "infinite"}, "statusIconPulse": {"duration": "1s", "easing": "ease-in-out", "iterations": "infinite", "property": "opacity", "values": "1, 0.6, 1"}, "statusIconSpin": {"duration": "1s", "easing": "linear", "iterations": "infinite", "property": "transform", "values": "rotate(0deg) to rotate(360deg)"}}, "typography": {"primary": {"fontFamily": "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif", "fontWeight": "400", "letterSpacing": "0.01em"}, "heading": {"fontSize": "24px", "fontWeight": "600", "lineHeight": "1.2"}, "body": {"fontSize": "18px", "fontWeight": "400", "lineHeight": "1.5"}, "interface": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.4"}, "mono": {"fontFamily": "SF Mono, Monaco, Consolas, monospace", "fontSize": "12px", "fontWeight": "500"}}, "spacing": {"xs": "4px", "sm": "8px", "md": "12px", "lg": "16px", "xl": "24px", "xxl": "32px"}, "borderRadius": {"small": "4px", "medium": "8px", "large": "16px", "full": "50%"}, "shadows": {"modal": "0 8px 32px rgba(0, 0, 0, 0.3)", "card": "0 4px 16px rgba(0, 0, 0, 0.2)", "subtle": "0 2px 8px rgba(0, 0, 0, 0.1)"}, "implementationRules": {"DO": ["Use exact hex values provided for each element context", "Apply waveform colors based on current state (idle/recording/processing)", "Maintain proper contrast ratios for text readability", "Use state-specific animations for status indicators", "Apply hover states to interactive elements", "Use monospace font for keyboard shortcuts", "Maintain consistent spacing using the defined scale", "Apply appropriate border radius to container elements"], "DO_NOT": ["Apply waveform colors to text elements", "Use recording red color for non-recording states", "Apply container backgrounds to individual UI elements", "Mix state-specific styling (recording colors in processing state)", "Use body text colors for interactive elements", "Apply shadows to individual bars or small elements", "Use processing animations in recording state", "Apply button hover colors as default colors"]}, "responsiveGuidelines": {"minWidth": "600px", "maxWidth": "900px", "mobileBreakpoint": "768px", "tabletBreakpoint": "1024px", "scaleFactors": {"mobile": "0.9", "tablet": "0.95", "desktop": "1.0"}}}