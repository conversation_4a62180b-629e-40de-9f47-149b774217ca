import sys
import math
import random
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor, QPalette, QLinearGradient
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QGraphicsDropShadowEffect, QGraphicsOpacityEffect)


class DesignSystemWaveform(QWidget):
    """Waveform visualization following exact JSON design specifications."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(600, 120)  # From JSON: height: "120px"
        self.bars = []
        self.num_bars = 200  # More bars for smoother visualization
        self.max_height = 60  # From JSON: varies 8px-60px for recording
        self.state = "idle"  # idle, recording, processing
        self.audio_level = 0

        # Initialize bars with minimum height
        for i in range(self.num_bars):
            self.bars.append(4)  # From JSON: varies 4px-40px for idle

        # Animation timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_waveform)
        self.timer.start(50)  # Smooth 20fps updates
        
    def set_state(self, state):
        """Set waveform state: idle, recording, processing."""
        self.state = state
        
    def update_audio_level(self, level):
        """Update with real audio level from microphone."""
        self.audio_level = level
        
    def update_waveform(self):
        """Update waveform bars based on current state."""
        if self.state == "idle":
            # From JSON: varies 4px-40px, opacity 0.6
            for i in range(len(self.bars)):
                target_height = random.randint(4, 40)
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 1)
                
        elif self.state == "recording":
            # From JSON: varies 8px-60px, with pulse animation
            base_level = self.audio_level / 100.0
            for i in range(len(self.bars)):
                # Create realistic voice pattern
                wave_factor = math.sin((i * 0.15) + (self.timer.interval() * 0.02)) * 0.4 + 0.6
                voice_factor = base_level * wave_factor * (random.random() * 0.3 + 0.7)
                target_height = int(voice_factor * 52) + 8  # 8px minimum + up to 52px
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 3)
                
        elif self.state == "processing":
            # From JSON: varies 12px-80px, with wave animation
            for i in range(len(self.bars)):
                wave_factor = math.sin((i * 0.1) + (self.timer.interval() * 0.03)) * 0.5 + 0.5
                target_height = int(wave_factor * 68) + 12  # 12px minimum + up to 68px
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 2)
                    
        self.update()
        
    def _smooth_transition(self, current, target, speed):
        """Smooth transition between current and target height."""
        if current < target:
            return min(target, current + speed)
        else:
            return max(target, current - speed)

    def paintEvent(self, event):
        """Paint waveform bars with design system colors."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Colors from JSON design system
        colors = {
            "idle": QColor(72, 72, 74),      # #48484A
            "recording": QColor(255, 59, 48), # #FF3B30
            "processing": QColor(0, 122, 255) # #007AFF
        }
        
        opacities = {
            "idle": 0.6,
            "recording": 1.0,
            "processing": 0.8
        }
        
        color = colors.get(self.state, colors["idle"])
        opacity = opacities.get(self.state, 0.6)
        color.setAlphaF(opacity)
        
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)
        
        # Draw bars with 2px width and 1px spacing (from JSON)
        bar_width = 2
        bar_spacing = 1
        total_width = self.num_bars * (bar_width + bar_spacing) - bar_spacing
        start_x = (self.width() - total_width) // 2
        
        for i, height in enumerate(self.bars):
            x = start_x + i * (bar_width + bar_spacing)
            y = (self.height() - height) // 2
            painter.drawRect(x, y, bar_width, height)


class DesignSystemStatusIndicator(QWidget):
    """Status indicator following JSON design specifications."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(20, 20)  # From JSON: size: "20px"
        self.state = "idle"  # idle, recording, processing, done
        
        # Animation timer for pulsing and spinning
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update)
        self.animation_timer.start(50)
        self.animation_phase = 0
        
    def set_state(self, state):
        """Set status state: idle, recording, processing, done."""
        self.state = state
        
    def paintEvent(self, event):
        """Paint status indicator with design system styling."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Colors from JSON design system
        colors = {
            "recording": QColor(255, 59, 48),  # #FF3B30
            "processing": QColor(0, 122, 255), # #007AFF
            "done": QColor(48, 209, 88)        # #30D158
        }
        
        if self.state in colors:
            color = colors[self.state]
            
            # Apply animations based on state
            if self.state == "recording":
                # Pulse animation
                self.animation_phase += 0.1
                opacity = 0.6 + 0.4 * math.sin(self.animation_phase)
                color.setAlphaF(opacity)
            elif self.state == "processing":
                # Spinning animation (visual effect through opacity variation)
                self.animation_phase += 0.2
                opacity = 0.7 + 0.3 * math.sin(self.animation_phase)
                color.setAlphaF(opacity)
            else:
                color.setAlphaF(1.0)
                
            painter.setBrush(QBrush(color))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(0, 0, 20, 20)


class DesignSystemRecordingWindow(QWidget):
    """Recording window following exact JSON design system specifications."""
    
    # Signals
    stop_recording = pyqtSignal()
    cancel_recording = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.state = "idle"  # idle, recording, processing, complete
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """Initialize UI following JSON design specifications."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        
        # From JSON: minWidth: "600px", maxWidth: "900px"
        self.setFixedSize(700, 200)
        
        # Main container with design system styling
        self.main_container = QWidget()
        self.apply_design_system_styling()
        
        # Shadow effect from JSON: "0 8px 32px rgba(0, 0, 0, 0.3)"
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(32)
        shadow.setColor(QColor(0, 0, 0, 77))  # 0.3 * 255 = 77
        shadow.setOffset(0, 8)
        self.main_container.setGraphicsEffect(shadow)
        
        # Main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)
        
        # Content layout with JSON spacing
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(24, 24, 24, 24)  # From JSON: padding: "24px"
        layout.setSpacing(24)  # From JSON: xl: "24px"

        # Status indicator section
        self.create_status_section(layout)
        
        # Waveform visualization
        self.waveform = DesignSystemWaveform()
        layout.addWidget(self.waveform, 0, Qt.AlignCenter)

        # Transcription area
        self.create_transcription_area(layout)
        
        # Control bar
        self.create_control_bar(layout)
        
        # Make window draggable
        self.make_draggable()
        
    def apply_design_system_styling(self):
        """Apply design system styling from JSON."""
        # From JSON mainContainer styling
        self.main_container.setStyleSheet(f"""
            QWidget {{
                background: #2C2C2E;
                border-radius: 16px;
                border: 1px solid #48484A;
            }}
        """)
        
    def create_status_section(self, layout):
        """Create status indicator section."""
        status_container = QWidget()
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(12)  # From JSON: gap: "12px"
        
        # Status indicator
        self.status_indicator = DesignSystemStatusIndicator()
        status_layout.addWidget(self.status_indicator)
        
        # Status text
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-weight: 500;
                font-size: 16px;
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        layout.addWidget(status_container)
        
    def create_transcription_area(self, layout):
        """Create transcription display area."""
        self.transcription_label = QLabel("Ready to record...")
        self.transcription_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                line-height: 1.5;
                font-weight: 400;
                letter-spacing: 0.01em;
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
                padding: 24px 0;
                min-height: 100px;
            }
        """)
        self.transcription_label.setAlignment(Qt.AlignCenter)
        self.transcription_label.setWordWrap(True)
        layout.addWidget(self.transcription_label)
        
    def create_control_bar(self, layout):
        """Create control bar with design system buttons."""
        control_container = QWidget()
        control_container.setStyleSheet("""
            QWidget {
                border-top: 1px solid #38383A;
                padding-top: 16px;
            }
        """)
        
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 16, 0, 0)
        control_layout.setSpacing(24)
        
        # Left section
        left_section = QWidget()
        left_layout = QHBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(16)
        
        # Mode button
        mode_btn = self.create_action_button("Mode")
        left_layout.addWidget(mode_btn)
        
        # Keyboard shortcut
        shortcut_container = QWidget()
        shortcut_layout = QHBoxLayout(shortcut_container)
        shortcut_layout.setContentsMargins(0, 0, 0, 0)
        shortcut_layout.setSpacing(4)
        
        cmd_key = QLabel("⌘")
        cmd_key.setStyleSheet(self.get_key_style())
        k_key = QLabel("K")
        k_key.setStyleSheet(self.get_key_style())
        
        shortcut_layout.addWidget(cmd_key)
        shortcut_layout.addWidget(k_key)
        left_layout.addWidget(shortcut_container)
        
        control_layout.addWidget(left_section)
        control_layout.addStretch()
        
        # Right section
        right_section = QWidget()
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)
        
        # Record/Stop button
        self.record_btn = self.create_action_button("Record")
        self.record_btn.clicked.connect(self.toggle_recording)
        right_layout.addWidget(self.record_btn)
        
        # Space shortcut
        space_key = QLabel("Space")
        space_key.setStyleSheet(self.get_key_style())
        right_layout.addWidget(space_key)
        
        # Close/Cancel button
        self.close_btn = self.create_action_button("Close")
        self.close_btn.clicked.connect(self.close_window)
        right_layout.addWidget(self.close_btn)
        
        # Esc shortcut
        esc_key = QLabel("Esc")
        esc_key.setStyleSheet(self.get_key_style())
        right_layout.addWidget(esc_key)
        
        control_layout.addWidget(right_section)
        layout.addWidget(control_container)

    def create_action_button(self, text):
        """Create action button with design system styling."""
        button = QPushButton(text)
        button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #EBEBF5;
                font-size: 16px;
                font-weight: 400;
                padding: 8px 16px;
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
            QPushButton:pressed {
                color: #FF3B30;
            }
        """)
        return button

    def get_key_style(self):
        """Get keyboard shortcut key styling from design system."""
        return """
            QLabel {
                background: #48484A;
                color: #EBEBF5;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                font-family: 'SF Mono', Monaco, monospace;
            }
        """

    def setup_animations(self):
        """Setup fade animations."""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None

    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)

    def mouseReleaseEvent(self, event):
        """Handle mouse release."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = None

    def position_center_screen(self):
        """Position window at center of screen."""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def set_state(self, state):
        """Set window state and update UI accordingly."""
        self.state = state
        self.waveform.set_state(state)
        self.status_indicator.set_state(state)

        # Update UI based on state
        if state == "idle":
            self.status_label.setText("")
            self.transcription_label.setText("Ready to record...")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

        elif state == "recording":
            self.status_label.setText("Recording")
            self.transcription_label.setText("Listening...")
            self.record_btn.setText("Stop")
            self.close_btn.setText("Cancel")

        elif state == "processing":
            self.status_label.setText("Processing")
            self.transcription_label.setText("Transcribing audio...")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

        elif state == "complete":
            self.status_label.setText("Done")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

    def start_recording(self):
        """Start recording with smooth animation."""
        self.set_state("recording")

        # Ensure window is visible and on top
        self.setWindowOpacity(1.0)  # Start fully visible for debugging
        self.show()
        self.raise_()  # Bring to front
        self.activateWindow()  # Activate the window

        # Optional fade in animation (disabled for now to ensure visibility)
        # self.setWindowOpacity(0)
        # self.fade_animation.setStartValue(0)
        # self.fade_animation.setEndValue(1)
        # self.fade_animation.start()

    def stop_recording_animation(self):
        """Stop recording animation."""
        self.set_state("processing")

    def update_audio_level(self, level):
        """Update audio level for waveform."""
        self.waveform.update_audio_level(level)

    def set_transcription(self, text):
        """Set transcription text."""
        self.transcription_label.setText(text)
        self.set_state("complete")

    def toggle_recording(self):
        """Toggle recording state."""
        if self.state == "recording":
            self.stop_recording.emit()
        else:
            self.start_recording()

    def close_window(self):
        """Close window with appropriate action."""
        if self.state == "recording":
            self.cancel_recording.emit()
        else:
            self.hide()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DesignSystemRecordingWindow()
    window.show()
    sys.exit(app.exec_())
