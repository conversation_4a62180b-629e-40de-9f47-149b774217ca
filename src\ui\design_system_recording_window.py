import sys
import math
import random
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor, QPalette, QLinearGradient
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QGraphicsDropShadowEffect, QGraphicsOpacityEffect)


class DesignSystemWaveform(QWidget):
    """Waveform visualization following exact JSON design specifications."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(700, 80)  # Wider, shorter like reference
        self.bars = []
        self.num_bars = 150  # Fewer bars for cleaner look
        self.max_height = 50  # Shorter bars
        self.state = "idle"  # idle, recording, processing
        self.audio_level = 0

        # Initialize bars with minimum height
        for i in range(self.num_bars):
            self.bars.append(2)  # Smaller minimum height

        # Animation timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_waveform)
        self.timer.start(60)  # Slightly slower for smoother look
        
    def set_state(self, state):
        """Set waveform state: idle, recording, processing."""
        self.state = state
        
    def update_audio_level(self, level):
        """Update with real audio level from microphone."""
        self.audio_level = level
        
    def update_waveform(self):
        """Update waveform bars to match reference screenshots."""
        if self.state == "idle":
            # Minimal activity for idle state
            for i in range(len(self.bars)):
                target_height = random.randint(2, 8)
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 1)

        elif self.state == "recording":
            # Active waveform during recording like reference
            base_level = max(0.3, self.audio_level / 100.0)  # Minimum activity
            for i in range(len(self.bars)):
                # Create more realistic voice pattern
                center_factor = 1.0 - abs(i - len(self.bars)/2) / (len(self.bars)/2) * 0.5
                wave_factor = math.sin((i * 0.2) + (self.timer.interval() * 0.05)) * 0.5 + 0.5
                voice_factor = base_level * center_factor * wave_factor * (random.random() * 0.4 + 0.8)
                target_height = int(voice_factor * 40) + 5  # 5px minimum + up to 40px
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 4)

        elif self.state == "processing":
            # Smooth wave animation for processing like reference
            for i in range(len(self.bars)):
                wave_factor = math.sin((i * 0.15) + (self.timer.interval() * 0.08)) * 0.6 + 0.4
                target_height = int(wave_factor * 30) + 8  # 8px minimum + up to 30px
                self.bars[i] = self._smooth_transition(self.bars[i], target_height, 2)

        self.update()
        
    def _smooth_transition(self, current, target, speed):
        """Smooth transition between current and target height."""
        if current < target:
            return min(target, current + speed)
        else:
            return max(target, current - speed)

    def paintEvent(self, event):
        """Paint waveform bars like reference screenshots."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Colors matching reference screenshots
        colors = {
            "idle": QColor(120, 120, 120),     # Gray for idle
            "recording": QColor(255, 80, 80),  # Red for recording
            "processing": QColor(100, 150, 255) # Blue for processing
        }

        opacities = {
            "idle": 0.4,
            "recording": 0.9,
            "processing": 0.7
        }

        color = colors.get(self.state, colors["idle"])
        opacity = opacities.get(self.state, 0.4)
        color.setAlphaF(opacity)

        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)

        # Draw bars with rounded caps like reference
        bar_width = 3
        bar_spacing = 2
        total_width = self.num_bars * (bar_width + bar_spacing) - bar_spacing
        start_x = (self.width() - total_width) // 2

        for i, height in enumerate(self.bars):
            x = start_x + i * (bar_width + bar_spacing)
            y = (self.height() - height) // 2
            # Draw rounded rectangle for smoother look
            painter.drawRoundedRect(x, y, bar_width, height, 1, 1)


class DesignSystemStatusIndicator(QWidget):
    """Status indicator matching reference screenshots."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(16, 16)  # Smaller like reference
        self.state = "idle"  # idle, recording, processing, done

        # Animation timer for pulsing and spinning
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update)
        self.animation_timer.start(100)  # Slower animation
        self.animation_phase = 0

    def set_state(self, state):
        """Set status state: idle, recording, processing, done."""
        self.state = state

    def paintEvent(self, event):
        """Paint status indicator matching reference screenshots."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Colors matching reference screenshots
        colors = {
            "recording": QColor(255, 80, 80),   # Red circle for recording
            "processing": QColor(100, 150, 255), # Blue circle for processing
            "done": QColor(80, 200, 120)        # Green checkmark for done
        }

        if self.state in colors:
            color = colors[self.state]

            # Apply animations based on state
            if self.state == "recording":
                # Pulse animation for recording
                self.animation_phase += 0.15
                opacity = 0.7 + 0.3 * math.sin(self.animation_phase)
                color.setAlphaF(opacity)
            elif self.state == "processing":
                # Subtle pulse for processing
                self.animation_phase += 0.1
                opacity = 0.8 + 0.2 * math.sin(self.animation_phase)
                color.setAlphaF(opacity)
            else:
                color.setAlphaF(1.0)

            painter.setBrush(QBrush(color))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(2, 2, 12, 12)  # Smaller circle with margin


class DesignSystemRecordingWindow(QWidget):
    """Recording window following exact JSON design system specifications."""
    
    # Signals
    stop_recording = pyqtSignal()
    cancel_recording = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.state = "idle"  # idle, recording, processing, complete
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """Initialize UI to match reference screenshots."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)

        # Larger size to match reference screenshots
        self.setFixedSize(800, 300)

        # Main container with modern dark styling
        self.main_container = QWidget()
        self.apply_modern_styling()

        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(40)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 10)
        self.main_container.setGraphicsEffect(shadow)

        # Main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)

        # Content layout - more spacious like reference
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(20)

        # Top section with status
        self.create_top_status_section(layout)

        # Large waveform visualization (main focus)
        self.waveform = DesignSystemWaveform()
        layout.addWidget(self.waveform, 0, Qt.AlignCenter)

        # Transcription area (larger, more prominent)
        self.create_main_transcription_area(layout)

        # Bottom control bar
        self.create_bottom_control_bar(layout)

        # Make window draggable
        self.make_draggable()
        
    def apply_modern_styling(self):
        """Apply modern dark styling to match reference screenshots."""
        self.main_container.setStyleSheet("""
            QWidget {
                background: rgba(60, 60, 60, 0.95);
                border-radius: 20px;
                border: 1px solid rgba(80, 80, 80, 0.8);
            }
        """)
        
    def create_top_status_section(self, layout):
        """Create top status section like reference screenshots."""
        status_container = QWidget()
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(12)

        # Status indicator (larger, more prominent)
        self.status_indicator = DesignSystemStatusIndicator()
        status_layout.addWidget(self.status_indicator)

        # Status text (larger, cleaner font)
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-weight: 600;
                font-size: 18px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
        """)
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()
        layout.addWidget(status_container)
        
    def create_main_transcription_area(self, layout):
        """Create main transcription display area like reference screenshots."""
        self.transcription_label = QLabel("Ready to record...")
        self.transcription_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                line-height: 1.6;
                font-weight: 400;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                padding: 20px;
                min-height: 80px;
                background: rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.transcription_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.transcription_label.setWordWrap(True)
        layout.addWidget(self.transcription_label)
        
    def create_bottom_control_bar(self, layout):
        """Create bottom control bar like reference screenshots."""
        control_container = QWidget()
        control_container.setStyleSheet("""
            QWidget {
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                padding-top: 16px;
            }
        """)

        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 16, 0, 0)
        control_layout.setSpacing(20)

        # Left section - Mode and shortcuts
        left_section = QWidget()
        left_layout = QHBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(20)

        # Mode button (like "Email" in reference)
        mode_btn = self.create_modern_button("Email")
        left_layout.addWidget(mode_btn)

        # Keyboard shortcut ⌘K
        cmd_shortcut = self.create_shortcut_display("⌘", "K")
        left_layout.addWidget(cmd_shortcut)

        control_layout.addWidget(left_section)
        control_layout.addStretch()

        # Right section - Action buttons
        right_section = QWidget()
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(20)

        # Record/Stop button
        self.record_btn = self.create_modern_button("Record")
        self.record_btn.clicked.connect(self.toggle_recording)
        right_layout.addWidget(self.record_btn)

        # Space shortcut
        space_shortcut = self.create_shortcut_display("Space")
        right_layout.addWidget(space_shortcut)

        # Close/Cancel button
        self.close_btn = self.create_modern_button("Close")
        self.close_btn.clicked.connect(self.close_window)
        right_layout.addWidget(self.close_btn)

        # Esc shortcut
        esc_shortcut = self.create_shortcut_display("Esc")
        right_layout.addWidget(esc_shortcut)

        control_layout.addWidget(right_section)
        layout.addWidget(control_container)

    def create_modern_button(self, text):
        """Create modern button like reference screenshots."""
        button = QPushButton(text)
        button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.8);
                font-size: 15px;
                font-weight: 500;
                padding: 8px 12px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
            QPushButton:hover {
                color: #FFFFFF;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.2);
            }
        """)
        return button

    def create_shortcut_display(self, *keys):
        """Create keyboard shortcut display like reference screenshots."""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)

        for key in keys:
            key_label = QLabel(key)
            key_label.setStyleSheet("""
                QLabel {
                    background: rgba(255, 255, 255, 0.15);
                    color: rgba(255, 255, 255, 0.8);
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                }
            """)
            layout.addWidget(key_label)

        return container

    def setup_animations(self):
        """Setup fade animations."""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None

    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)

    def mouseReleaseEvent(self, event):
        """Handle mouse release."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = None

    def position_center_screen(self):
        """Position window at center of screen."""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def set_state(self, state):
        """Set window state and update UI to match reference screenshots."""
        self.state = state
        self.waveform.set_state(state)
        self.status_indicator.set_state(state)

        # Update UI based on state to match reference screenshots
        if state == "idle":
            self.status_label.setText("")
            self.transcription_label.setText("Ready to record...")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

        elif state == "recording":
            # Match first reference screenshot
            self.status_label.setText("Recording")
            self.transcription_label.setText("Listening...")
            self.record_btn.setText("Stop")
            self.close_btn.setText("Cancel")

        elif state == "processing":
            # Match second reference screenshot
            self.status_label.setText("Processing")
            self.transcription_label.setText("Processing...")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

        elif state == "complete":
            # Match third reference screenshot
            self.status_label.setText("Done")
            self.record_btn.setText("Record")
            self.close_btn.setText("Close")

    def start_recording(self):
        """Start recording with smooth animation."""
        self.set_state("recording")

        # Ensure window is visible and on top
        self.setWindowOpacity(1.0)  # Start fully visible for debugging
        self.show()
        self.raise_()  # Bring to front
        self.activateWindow()  # Activate the window

        # Optional fade in animation (disabled for now to ensure visibility)
        # self.setWindowOpacity(0)
        # self.fade_animation.setStartValue(0)
        # self.fade_animation.setEndValue(1)
        # self.fade_animation.start()

    def stop_recording_animation(self):
        """Stop recording animation."""
        self.set_state("processing")

    def update_audio_level(self, level):
        """Update audio level for waveform."""
        self.waveform.update_audio_level(level)

    def set_transcription(self, text):
        """Set transcription text like reference screenshots."""
        # Format text nicely for display
        formatted_text = text.strip()
        if formatted_text:
            self.transcription_label.setText(formatted_text)
        else:
            self.transcription_label.setText("No transcription available")
        self.set_state("complete")

    def toggle_recording(self):
        """Toggle recording state."""
        if self.state == "recording":
            self.stop_recording.emit()
        else:
            self.start_recording()

    def close_window(self):
        """Close window with appropriate action."""
        if self.state == "recording":
            self.cancel_recording.emit()
        else:
            self.hide()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DesignSystemRecordingWindow()
    window.show()
    sys.exit(app.exec_())
